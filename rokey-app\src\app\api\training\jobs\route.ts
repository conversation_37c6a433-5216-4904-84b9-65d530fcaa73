import { type NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';
import { type TrainingJob, type NewTrainingJob } from '@/types/training';

// GET /api/training/jobs?custom_api_config_id=<ID>&active_only=true
// Retrieves training jobs for a specific custom_api_config_id
// If active_only=true, returns only the most recent completed training data for chat enhancement
export async function GET(request: NextRequest) {
  const supabase = await createSupabaseServerClientOnRequest();

  // Check authentication
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    console.error('Authentication failed in /api/training/jobs:', authError);
    return NextResponse.json({ error: 'Unauthorized: You must be logged in to view training jobs.' }, { status: 401 });
  }

  const { searchParams } = new URL(request.url);
  const customApiConfigId = searchParams.get('custom_api_config_id');
  const activeOnly = searchParams.get('active_only') === 'true';

  if (!customApiConfigId) {
    return NextResponse.json({ error: 'custom_api_config_id query parameter is required' }, { status: 400 });
  }

  try {
    let query = supabase
      .from('training_jobs')
      .select('*')
      .eq('custom_api_config_id', customApiConfigId);

    if (activeOnly) {
      // Get only the most recent completed training job for chat enhancement
      query = query
        .eq('status', 'completed')
        .order('created_at', { ascending: false })
        .limit(1);
    } else {
      // Get all training jobs for management view
      query = query.order('created_at', { ascending: false });
    }

    const { data: jobs, error } = await query;

    if (error) {
      console.error('Supabase error fetching training jobs:', error);
      return NextResponse.json({ error: 'Failed to fetch training jobs', details: error.message }, { status: 500 });
    }

    if (activeOnly && jobs && jobs.length > 0) {
      // Return the training data in a format ready for chat enhancement
      const latestJob = jobs[0];

      return NextResponse.json({
        has_training: true,
        training_data: latestJob.training_data,
        job_id: latestJob.id,
        created_at: latestJob.created_at
      }, { status: 200 });
    }

    return NextResponse.json(activeOnly ? { has_training: false } : (jobs || []), { status: 200 });

  } catch (e: any) {
    console.error('Error in GET /api/training/jobs:', e);
    return NextResponse.json({ error: 'An unexpected error occurred', details: e.message }, { status: 500 });
  }
}

// POST /api/training/jobs
// Creates a new training job - WITH SAFEGUARD AGAINST DUPLICATE CREATION
export async function POST(request: NextRequest) {
  const supabase = await createSupabaseServerClientOnRequest();

  // Check authentication
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    console.error('Authentication failed in /api/training/jobs:', authError);
    return NextResponse.json({ error: 'Unauthorized: You must be logged in to create training jobs.' }, { status: 401 });
  }

  try {
    const jobData = await request.json() as NewTrainingJob;
    const { custom_api_config_id, name, description, training_data, parameters } = jobData;

    if (!custom_api_config_id || !name) {
      return NextResponse.json({ error: 'Missing required fields: custom_api_config_id, name' }, { status: 400 });
    }

    // SAFEGUARD: Check if a training job already exists for this config
    // This prevents accidental creation of duplicate jobs that would trigger CASCADE DELETE
    console.log(`[Training Job Creation] Checking for existing jobs for config: ${custom_api_config_id}`);
    const { data: existingJobs, error: checkError } = await supabase
      .from('training_jobs')
      .select('id, name, status, created_at')
      .eq('custom_api_config_id', custom_api_config_id)
      .order('created_at', { ascending: false })
      .limit(1);

    if (checkError) {
      console.error('[Training Job Creation] Error checking for existing jobs:', checkError);
      // Continue with creation if check fails - don't block the operation
    } else if (existingJobs && existingJobs.length > 0) {
      const existingJob = existingJobs[0];
      console.warn(`[Training Job Creation] WARNING: Training job already exists for config ${custom_api_config_id}:`, existingJob);
      console.warn('[Training Job Creation] Consider using PUT to update instead of creating new job');

      // Return the existing job instead of creating a new one
      // This prevents CASCADE DELETE of training files
      return NextResponse.json({
        ...existingJob,
        warning: 'Training job already exists for this configuration. Returned existing job to prevent data loss.',
        recommendation: 'Use PUT method to update existing training job instead.'
      }, { status: 200 });
    }

    console.log(`[Training Job Creation] No existing job found, creating new training job for config: ${custom_api_config_id}`);
    const { data, error } = await supabase
      .from('training_jobs')
      .insert({
        custom_api_config_id,
        name,
        description,
        training_data,
        parameters,
        status: 'completed', // Prompt engineering is instant
        progress_percentage: 100,
        started_at: new Date().toISOString(),
        completed_at: new Date().toISOString(),
        user_id: user.id, // Add authenticated user ID
      })
      .select()
      .single();

    if (error) {
      console.error('Supabase error creating training job:', error);
      return NextResponse.json({ error: 'Failed to create training job', details: error.message }, { status: 500 });
    }

    console.log(`[Training Job Creation] Successfully created new training job:`, data.id);
    return NextResponse.json(data, { status: 201 });

  } catch (e: any) {
    console.error('Error in POST /api/training/jobs:', e);
    if (e.name === 'SyntaxError') {
      return NextResponse.json({ error: 'Invalid request body: Malformed JSON.' }, { status: 400 });
    }
    return NextResponse.json({ error: 'An unexpected error occurred', details: e.message }, { status: 500 });
  }
}

// PUT /api/training/jobs?id=<ID>
// Updates a training job - ENHANCED WITH VALIDATION AND LOGGING
export async function PUT(request: NextRequest) {
  const supabase = await createSupabaseServerClientOnRequest();

  const { searchParams } = new URL(request.url);
  const jobId = searchParams.get('id');

  if (!jobId) {
    return NextResponse.json({ error: 'id query parameter is required' }, { status: 400 });
  }

  try {
    const updateData = await request.json();
    console.log(`[Training Job Update] Updating training job ${jobId} with data:`, updateData);

    // Verify the job exists before updating
    const { data: existingJob, error: checkError } = await supabase
      .from('training_jobs')
      .select('id, name, status, custom_api_config_id, created_at')
      .eq('id', jobId)
      .single();

    if (checkError) {
      console.error(`[Training Job Update] Error checking existing job ${jobId}:`, checkError);
      return NextResponse.json({
        error: 'Failed to verify training job exists',
        details: checkError.message
      }, { status: 500 });
    }

    if (!existingJob) {
      console.error(`[Training Job Update] Training job ${jobId} not found`);
      return NextResponse.json({ error: 'Training job not found' }, { status: 404 });
    }

    console.log(`[Training Job Update] Found existing job:`, existingJob);

    // Perform the update
    const { data, error } = await supabase
      .from('training_jobs')
      .update({
        ...updateData,
        updated_at: new Date().toISOString() // Ensure updated_at is always set
      })
      .eq('id', jobId)
      .select()
      .single();

    if (error) {
      console.error(`[Training Job Update] Supabase error updating training job ${jobId}:`, error);
      return NextResponse.json({
        error: 'Failed to update training job',
        details: error.message
      }, { status: 500 });
    }

    console.log(`[Training Job Update] Successfully updated training job ${jobId}:`, data);
    return NextResponse.json(data, { status: 200 });

  } catch (e: any) {
    console.error(`[Training Job Update] Error in PUT /api/training/jobs for job ${jobId}:`, e);
    if (e.name === 'SyntaxError') {
      return NextResponse.json({ error: 'Invalid request body: Malformed JSON.' }, { status: 400 });
    }
    return NextResponse.json({ error: 'An unexpected error occurred', details: e.message }, { status: 500 });
  }
}

// DELETE /api/training/jobs?id=<ID>
// Deletes a training job and all associated files
export async function DELETE(request: NextRequest) {
  const supabase = await createSupabaseServerClientOnRequest();
  
  const { searchParams } = new URL(request.url);
  const jobId = searchParams.get('id');

  if (!jobId) {
    return NextResponse.json({ error: 'id query parameter is required' }, { status: 400 });
  }

  try {
    // Delete training job (files will be deleted automatically due to CASCADE)
    const { error } = await supabase
      .from('training_jobs')
      .delete()
      .eq('id', jobId);

    if (error) {
      console.error('Supabase error deleting training job:', error);
      return NextResponse.json({ error: 'Failed to delete training job', details: error.message }, { status: 500 });
    }

    return NextResponse.json({ message: 'Training job deleted successfully' }, { status: 200 });

  } catch (e: any) {
    console.error('Error in DELETE /api/training/jobs:', e);
    return NextResponse.json({ error: 'An unexpected error occurred', details: e.message }, { status: 500 });
  }
}
