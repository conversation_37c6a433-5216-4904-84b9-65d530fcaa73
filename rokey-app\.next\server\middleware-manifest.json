{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "MQ6n+AXoa6Rux06XbSq+6wJ20O0xvuFjerK9xZRPHR4="}}}, "functions": {}, "sortedMiddleware": ["/"]}